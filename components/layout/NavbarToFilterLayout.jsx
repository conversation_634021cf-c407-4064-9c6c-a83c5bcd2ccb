import React from "react";
import Navbar from "@/components/navbar/Navbar";
import HeroInfo from "@/components/hero/HeroInfo";
import FilterBar from "@/components/hero/FilterBar";

const NavbarToFilterLayout = () => {
  return (
    <div
      className="w-full bg-cover bg-no-repeat bg-center rounded-t-2xl overflow-hidden min-h-[600px] relative"
      style={{ backgroundImage: "url(/backgroundImg.png)" }}
    >
      {/* Content */}
      <div className="relative z-10">
        <Navbar />
        <HeroInfo />
        <FilterBar />
      </div>
    </div>
  );
};

export default NavbarToFilterLayout;
